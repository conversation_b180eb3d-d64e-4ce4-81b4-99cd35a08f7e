"""
Chunked Upload Routes
API endpoints for handling chunked/resumable video uploads
"""
import os
import logging
from flask import Blueprint, request, jsonify
from werkzeug.utils import secure_filename
from utils.video_upload_manager import VideoUploadManager
from utils.file_validation import validate_upload_file
from utils.auth_middleware import token_required
from tasks.upload_manager import finalize_multipart_upload
from config.upload_config import get_frontend_config

logger = logging.getLogger(__name__)

# Create blueprint
chunked_upload_bp = Blueprint('chunked_upload', __name__)

@chunked_upload_bp.route('/initialize', methods=['POST'])
@token_required
def initialize_upload():
    """
    Initialize a chunked upload session
    
    Expected JSON payload:
    {
        "filename": "video.mp4",
        "file_size": 1073741824,
        "content_type": "video/mp4",
        "coordinates": "lat,lng",
        "selected_model": "All"
    }
    """
    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['filename', 'file_size', 'content_type']
        for field in required_fields:
            if field not in data:
                return jsonify({
                    'success': False,
                    'error': f'Missing required field: {field}'
                }), 400
        
        # Get user info from token
        username = request.user.get('username', 'unknown')
        role = request.user.get('role', 'user')
        
        # Validate file
        filename = secure_filename(data['filename'])
        file_size = int(data['file_size'])
        content_type = data['content_type']
        
        # Basic validation
        if not filename:
            return jsonify({
                'success': False,
                'error': 'Invalid filename'
            }), 400
        
        if file_size <= 0:
            return jsonify({
                'success': False,
                'error': 'Invalid file size'
            }), 400
        
        # Check file size limit
        config = get_frontend_config()
        if file_size > config['max_video_size']:
            return jsonify({
                'success': False,
                'error': f'File size exceeds maximum limit of {config["max_video_size"]} bytes'
            }), 400
        
        # Validate file type
        if not content_type.startswith('video/'):
            return jsonify({
                'success': False,
                'error': 'Only video files are allowed'
            }), 400
        
        # Initialize upload session
        upload_manager = VideoUploadManager()
        session_info = upload_manager.create_upload_session(
            filename=filename,
            file_size=file_size,
            content_type=content_type,
            username=username,
            role=role,
            coordinates=data.get('coordinates'),
            selected_model=data.get('selected_model', 'All')
        )
        
        logger.info(f"Initialized upload session: {session_info['upload_id']}")
        
        return jsonify({
            'success': True,
            'upload_id': session_info['upload_id'],
            'session_id': session_info['session_id'],
            'chunk_count': session_info['chunk_count'],
            'chunk_size': session_info['chunk_size'],
            'use_multipart': session_info['use_multipart']
        })
        
    except Exception as e:
        logger.error(f"Failed to initialize upload: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Failed to initialize upload: {str(e)}'
        }), 500

@chunked_upload_bp.route('/chunk', methods=['POST'])
@token_required
def upload_chunk():
    """
    Upload a single chunk
    
    Expected form data:
    - upload_id: Upload session ID
    - chunk_number: Chunk number (1-based)
    - chunk: File chunk data
    """
    try:
        # Get form data
        upload_id = request.form.get('upload_id')
        chunk_number = request.form.get('chunk_number')
        
        if not upload_id or not chunk_number:
            return jsonify({
                'success': False,
                'error': 'Missing upload_id or chunk_number'
            }), 400
        
        try:
            chunk_number = int(chunk_number)
        except ValueError:
            return jsonify({
                'success': False,
                'error': 'Invalid chunk_number'
            }), 400
        
        # Get chunk data
        if 'chunk' not in request.files:
            return jsonify({
                'success': False,
                'error': 'No chunk data provided'
            }), 400
        
        chunk_file = request.files['chunk']
        chunk_data = chunk_file.read()
        
        if not chunk_data:
            return jsonify({
                'success': False,
                'error': 'Empty chunk data'
            }), 400
        
        # Upload chunk
        upload_manager = VideoUploadManager()
        result = upload_manager.upload_chunk(upload_id, chunk_number, chunk_data)
        
        logger.info(f"Uploaded chunk {chunk_number} for {upload_id}")
        
        return jsonify({
            'success': True,
            'upload_id': upload_id,
            'chunk_number': chunk_number,
            'status': result['status'],
            'chunk_hash': result.get('chunk_hash')
        })
        
    except Exception as e:
        logger.error(f"Failed to upload chunk: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Failed to upload chunk: {str(e)}'
        }), 500

@chunked_upload_bp.route('/complete', methods=['POST'])
@token_required
def complete_upload():
    """
    Complete the chunked upload and start processing
    
    Expected JSON payload:
    {
        "upload_id": "uuid",
        "session_id": "uuid"
    }
    """
    try:
        data = request.get_json()
        
        upload_id = data.get('upload_id')
        session_id = data.get('session_id')
        
        if not upload_id or not session_id:
            return jsonify({
                'success': False,
                'error': 'Missing upload_id or session_id'
            }), 400
        
        # Get user info
        username = request.user.get('username', 'unknown')
        role = request.user.get('role', 'user')
        
        # Get upload session to retrieve coordinates and model
        upload_manager = VideoUploadManager()
        session = upload_manager._get_upload_session(upload_id)
        
        if not session:
            return jsonify({
                'success': False,
                'error': 'Upload session not found'
            }), 404
        
        # Start background task to finalize upload and process video
        task = finalize_multipart_upload.delay(
            upload_id=upload_id,
            session_id=session_id,
            username=username,
            role=role,
            coordinates=session.get('coordinates'),
            selected_model=session.get('selected_model', 'All')
        )
        
        logger.info(f"Started upload completion task: {task.id} for {upload_id}")
        
        return jsonify({
            'success': True,
            'upload_id': upload_id,
            'task_id': task.id,
            'message': 'Upload completion started',
            'status': 'processing'
        })
        
    except Exception as e:
        logger.error(f"Failed to complete upload: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Failed to complete upload: {str(e)}'
        }), 500

@chunked_upload_bp.route('/status/<upload_id>', methods=['GET'])
@token_required
def get_upload_status(upload_id):
    """
    Get upload status and progress
    """
    try:
        upload_manager = VideoUploadManager()
        status = upload_manager.get_upload_status(upload_id)
        
        return jsonify({
            'success': True,
            'upload_status': status
        })
        
    except Exception as e:
        logger.error(f"Failed to get upload status: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Failed to get upload status: {str(e)}'
        }), 500

@chunked_upload_bp.route('/abort', methods=['POST'])
@token_required
def abort_upload():
    """
    Abort an upload session
    
    Expected JSON payload:
    {
        "upload_id": "uuid"
    }
    """
    try:
        data = request.get_json()
        upload_id = data.get('upload_id')
        
        if not upload_id:
            return jsonify({
                'success': False,
                'error': 'Missing upload_id'
            }), 400
        
        upload_manager = VideoUploadManager()
        result = upload_manager.abort_upload(upload_id)
        
        logger.info(f"Aborted upload: {upload_id}")
        
        return jsonify({
            'success': True,
            'result': result
        })
        
    except Exception as e:
        logger.error(f"Failed to abort upload: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Failed to abort upload: {str(e)}'
        }), 500

@chunked_upload_bp.route('/config', methods=['GET'])
def get_upload_config():
    """
    Get upload configuration for frontend
    """
    try:
        config = get_frontend_config()
        return jsonify({
            'success': True,
            'config': config
        })
        
    except Exception as e:
        logger.error(f"Failed to get upload config: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Failed to get upload config: {str(e)}'
        }), 500
