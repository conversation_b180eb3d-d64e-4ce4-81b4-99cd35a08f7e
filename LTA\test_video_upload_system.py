#!/usr/bin/env python3
"""
Test Script for Production Video Upload System
Tests chunked uploads, background processing, and status tracking
"""

import os
import sys
import time
import json
import requests
import tempfile
from pathlib import Path

# Configuration
BASE_URL = "http://localhost:5000"
TEST_USERNAME = "test_user"
TEST_PASSWORD = "test_password"
TEST_COORDINATES = "1.234,5.678"
TEST_MODEL = "All"

class VideoUploadTester:
    def __init__(self, base_url=BASE_URL):
        self.base_url = base_url
        self.session = requests.Session()
        self.token = None
        self.upload_id = None
        self.job_id = None
        
    def authenticate(self, username=TEST_USERNAME, password=TEST_PASSWORD):
        """Authenticate and get JWT token"""
        print("🔐 Authenticating...")
        
        # First register user if needed
        try:
            register_data = {
                "username": username,
                "password": password,
                "role": "supervisor"
            }
            response = self.session.post(f"{self.base_url}/api/users/register", json=register_data)
            if response.status_code == 201:
                print(f"✅ User registered: {username}")
            elif response.status_code == 400 and "already exists" in response.text:
                print(f"ℹ️  User already exists: {username}")
            else:
                print(f"⚠️  Registration response: {response.status_code} - {response.text}")
        except Exception as e:
            print(f"⚠️  Registration error: {e}")
        
        # Login
        login_data = {"username": username, "password": password}
        response = self.session.post(f"{self.base_url}/api/users/login", json=login_data)
        
        if response.status_code == 200:
            data = response.json()
            self.token = data.get('access_token')
            self.session.headers.update({'Authorization': f'Bearer {self.token}'})
            print(f"✅ Authentication successful")
            return True
        else:
            print(f"❌ Authentication failed: {response.status_code} - {response.text}")
            return False
    
    def create_test_video(self, size_mb=50, duration_seconds=30):
        """Create a test video file"""
        print(f"🎬 Creating test video ({size_mb}MB, {duration_seconds}s)...")
        
        # Create a temporary file with the specified size
        temp_file = tempfile.NamedTemporaryFile(suffix='.mp4', delete=False)
        
        # Write dummy data to reach the desired size
        chunk_size = 1024 * 1024  # 1MB chunks
        total_chunks = size_mb
        
        for i in range(total_chunks):
            # Write some pattern data
            data = b'0' * chunk_size
            temp_file.write(data)
        
        temp_file.close()
        
        print(f"✅ Test video created: {temp_file.name} ({size_mb}MB)")
        return temp_file.name
    
    def test_upload_config(self):
        """Test upload configuration endpoint"""
        print("⚙️  Testing upload configuration...")
        
        response = self.session.get(f"{self.base_url}/api/upload/config")
        
        if response.status_code == 200:
            config = response.json()
            print(f"✅ Upload config retrieved:")
            print(f"   Max file size: {config.get('max_file_size', 0) / (1024**3):.1f}GB")
            print(f"   Max duration: {config.get('max_duration', 0) / 3600:.1f}h")
            print(f"   Chunk size: {config.get('chunk_size', 0) / (1024**2):.1f}MB")
            return True
        else:
            print(f"❌ Config test failed: {response.status_code}")
            return False
    
    def test_chunked_upload(self, video_file):
        """Test chunked upload process"""
        print("📤 Testing chunked upload...")
        
        file_size = os.path.getsize(video_file)
        filename = os.path.basename(video_file)
        
        # Step 1: Initialize upload
        print("   1. Initializing upload...")
        init_data = {
            "filename": filename,
            "file_size": file_size,
            "content_type": "video/mp4",
            "coordinates": TEST_COORDINATES,
            "selected_model": TEST_MODEL
        }
        
        response = self.session.post(f"{self.base_url}/api/upload/initialize", json=init_data)
        
        if response.status_code != 200:
            print(f"❌ Upload initialization failed: {response.status_code} - {response.text}")
            return False
        
        init_result = response.json()
        self.upload_id = init_result['upload_id']
        chunk_size = init_result.get('chunk_size', 10 * 1024 * 1024)
        chunk_count = init_result['chunk_count']
        
        print(f"   ✅ Upload initialized: {self.upload_id}")
        print(f"      Chunks: {chunk_count}, Size: {chunk_size / (1024**2):.1f}MB each")
        
        # Step 2: Upload chunks
        print("   2. Uploading chunks...")
        
        with open(video_file, 'rb') as f:
            for chunk_num in range(1, chunk_count + 1):
                chunk_data = f.read(chunk_size)
                if not chunk_data:
                    break
                
                files = {
                    'upload_id': (None, self.upload_id),
                    'chunk_number': (None, str(chunk_num)),
                    'chunk': (f'chunk_{chunk_num}', chunk_data, 'application/octet-stream')
                }
                
                response = self.session.post(f"{self.base_url}/api/upload/chunk", files=files)
                
                if response.status_code != 200:
                    print(f"❌ Chunk {chunk_num} upload failed: {response.status_code}")
                    return False
                
                progress = (chunk_num / chunk_count) * 100
                print(f"      Chunk {chunk_num}/{chunk_count} uploaded ({progress:.1f}%)")
        
        # Step 3: Complete upload
        print("   3. Completing upload...")
        complete_data = {
            "upload_id": self.upload_id,
            "session_id": init_result['session_id']
        }
        
        response = self.session.post(f"{self.base_url}/api/upload/complete", json=complete_data)
        
        if response.status_code != 200:
            print(f"❌ Upload completion failed: {response.status_code} - {response.text}")
            return False
        
        complete_result = response.json()
        self.job_id = complete_result.get('task_id')
        
        print(f"   ✅ Upload completed successfully!")
        print(f"      Job ID: {self.job_id}")
        
        return True
    
    def test_status_tracking(self, max_wait_time=300):
        """Test status tracking and polling"""
        print("📊 Testing status tracking...")
        
        if not self.job_id:
            print("❌ No job ID available for status tracking")
            return False
        
        start_time = time.time()
        last_status = None
        
        while time.time() - start_time < max_wait_time:
            # Check status by job ID
            response = self.session.get(f"{self.base_url}/api/status/job/{self.job_id}")
            
            if response.status_code != 200:
                print(f"❌ Status check failed: {response.status_code}")
                return False
            
            status_data = response.json()
            status = status_data.get('status', {})
            
            current_status = status.get('final_status', 'UNKNOWN')
            
            if current_status != last_status:
                print(f"   Status: {current_status}")
                
                db_status = status.get('database_status', {})
                if db_status:
                    progress = db_status.get('progress', 0)
                    message = db_status.get('message', '')
                    print(f"   Progress: {progress}% - {message}")
                
                last_status = current_status
            
            # Check if processing is complete
            if current_status in ['COMPLETED', 'FAILED']:
                if current_status == 'COMPLETED':
                    print("   ✅ Processing completed successfully!")
                    return True
                else:
                    error = status.get('database_status', {}).get('error', 'Unknown error')
                    print(f"   ❌ Processing failed: {error}")
                    return False
            
            time.sleep(5)  # Poll every 5 seconds
        
        print(f"   ⏰ Status tracking timed out after {max_wait_time}s")
        return False
    
    def test_user_jobs(self):
        """Test user jobs endpoint"""
        print("👤 Testing user jobs endpoint...")
        
        response = self.session.get(f"{self.base_url}/api/status/user/jobs")
        
        if response.status_code == 200:
            jobs = response.json().get('jobs', [])
            print(f"   ✅ Retrieved {len(jobs)} user jobs")
            
            for job in jobs[:3]:  # Show first 3 jobs
                print(f"      Job: {job.get('job_id', 'N/A')} - {job.get('status', 'N/A')}")
            
            return True
        else:
            print(f"   ❌ User jobs test failed: {response.status_code}")
            return False
    
    def cleanup(self, video_file):
        """Clean up test files"""
        print("🧹 Cleaning up...")
        
        try:
            if os.path.exists(video_file):
                os.unlink(video_file)
                print(f"   ✅ Deleted test video: {video_file}")
        except Exception as e:
            print(f"   ⚠️  Cleanup error: {e}")
    
    def run_full_test(self, video_size_mb=50):
        """Run complete test suite"""
        print("🚀 Starting Video Upload System Test")
        print("=" * 50)
        
        success = True
        video_file = None
        
        try:
            # Step 1: Authentication
            if not self.authenticate():
                return False
            
            # Step 2: Test configuration
            if not self.test_upload_config():
                success = False
            
            # Step 3: Create test video
            video_file = self.create_test_video(size_mb=video_size_mb)
            
            # Step 4: Test chunked upload
            if not self.test_chunked_upload(video_file):
                success = False
            
            # Step 5: Test status tracking
            if success and not self.test_status_tracking():
                success = False
            
            # Step 6: Test user jobs
            if not self.test_user_jobs():
                success = False
            
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            success = False
        
        finally:
            if video_file:
                self.cleanup(video_file)
        
        print("=" * 50)
        if success:
            print("🎉 All tests passed successfully!")
        else:
            print("💥 Some tests failed!")
        
        return success

def main():
    """Main test function"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Test Video Upload System')
    parser.add_argument('--url', default=BASE_URL, help='Base URL for API')
    parser.add_argument('--size', type=int, default=50, help='Test video size in MB')
    parser.add_argument('--username', default=TEST_USERNAME, help='Test username')
    parser.add_argument('--password', default=TEST_PASSWORD, help='Test password')
    
    args = parser.parse_args()
    
    # Update global variables
    global BASE_URL, TEST_USERNAME, TEST_PASSWORD
    BASE_URL = args.url
    TEST_USERNAME = args.username
    TEST_PASSWORD = args.password
    
    # Run tests
    tester = VideoUploadTester(args.url)
    success = tester.run_full_test(args.size)
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
