"""
Celery Tasks for Video Processing
"""
import os
import logging
import traceback
from datetime import datetime, timedelta
from celery import current_task
from celery_app import celery
from config.db import connect_to_db
from utils.video_upload_manager import VideoUploadManager
from utils.file_validation import validate_video_file
from s3_mongodb_integration import S3ImageManager
import cv2
import uuid
from bson import ObjectId

logger = logging.getLogger(__name__)

@celery.task(bind=True, name='tasks.video_processing.process_video_task')
def process_video_task(self, upload_id: str, session_id: str, username: str, role: str, 
                      coordinates: str = None, selected_model: str = 'All'):
    """
    Background task to process uploaded video
    
    Args:
        upload_id: Unique upload identifier
        session_id: Upload session ID
        username: User who uploaded the video
        role: User role
        coordinates: GPS coordinates
        selected_model: Model type for processing
    """
    job_id = self.request.id
    
    try:
        # Update task status to STARTED
        update_job_status(job_id, 'PROCESSING', 'Starting video processing...', 0)
        
        # Get database connection
        db = connect_to_db()
        if db is None:
            raise Exception("Database connection failed")
        
        # Get upload session
        session = db.upload_sessions.find_one({'session_id': session_id})
        if not session:
            raise Exception(f"Upload session {session_id} not found")
        
        if not session.get('final_file_path') or not os.path.exists(session['final_file_path']):
            raise Exception("Uploaded video file not found")
        
        video_path = session['final_file_path']
        original_filename = session['filename']
        
        # Validate video file
        update_job_status(job_id, 'PROCESSING', 'Validating video file...', 10)
        
        # Create a mock file object for validation
        class MockFile:
            def __init__(self, path, filename):
                self.path = path
                self.filename = filename
                self._size = os.path.getsize(path)
            
            @property
            def content_type(self):
                return 'video/mp4'  # Default, could be improved
        
        mock_file = MockFile(video_path, original_filename)
        is_valid, error_msg = validate_video_file(mock_file)
        
        if not is_valid:
            raise Exception(f"Video validation failed: {error_msg}")
        
        # Get video metadata
        update_job_status(job_id, 'PROCESSING', 'Extracting video metadata...', 20)
        
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            raise Exception("Could not open video file")
        
        fps = cap.get(cv2.CAP_PROP_FPS)
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        duration = frame_count / fps if fps > 0 else 0
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        cap.release()
        
        # Check duration limit (1 hour = 3600 seconds)
        if duration > 3600:
            raise Exception(f"Video duration {duration:.2f}s exceeds maximum allowed duration of 3600s")
        
        # Create video processing job record
        video_job = {
            'job_id': job_id,
            'upload_id': upload_id,
            'session_id': session_id,
            'username': username,
            'role': role,
            'original_filename': original_filename,
            'video_path': video_path,
            'coordinates': coordinates,
            'selected_model': selected_model,
            'status': 'PROCESSING',
            'progress': 20,
            'message': 'Processing video...',
            'metadata': {
                'duration': duration,
                'fps': fps,
                'frame_count': frame_count,
                'width': width,
                'height': height,
                'file_size': session['file_size']
            },
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow(),
            'original_video_s3_url': None,
            'processed_video_s3_url': None,
            'results': None,
            'error': None
        }
        
        result = db.video_processing_jobs.insert_one(video_job)
        job_doc_id = result.inserted_id
        
        # Upload original video to S3
        update_job_status(job_id, 'PROCESSING', 'Uploading original video to S3...', 30)
        
        s3_manager = S3ImageManager()
        
        # Read video file
        with open(video_path, 'rb') as f:
            video_data = f.read()
        
        # Create S3 key for original video
        original_s3_key = f"{role}/{username}/videos/original/{upload_id}_{original_filename}"
        
        upload_success, s3_result = s3_manager.upload_image_to_s3(
            video_data, original_s3_key, content_type='video/mp4'
        )
        
        if not upload_success:
            raise Exception(f"Failed to upload original video to S3: {s3_result}")
        
        original_s3_url = s3_result
        
        # Update job with S3 URL
        db.video_processing_jobs.update_one(
            {'_id': job_doc_id},
            {'$set': {'original_video_s3_url': original_s3_url, 'updated_at': datetime.utcnow()}}
        )
        
        # Process video with AI models
        update_job_status(job_id, 'PROCESSING', 'Running AI analysis on video...', 50)
        
        # Import video processing function
        from routes.pavement import process_pavement_video
        
        # Create output directory for processed video
        processed_dir = os.path.join(os.path.dirname(video_path), 'processed')
        os.makedirs(processed_dir, exist_ok=True)
        
        processed_video_path = os.path.join(processed_dir, f"processed_{upload_id}_{original_filename}")
        
        # Process video (this is a simplified version - you may need to adapt based on your existing processing logic)
        try:
            # This would call your existing video processing pipeline
            # For now, we'll create a placeholder processed video
            
            # Copy original video as processed (placeholder)
            import shutil
            shutil.copy2(video_path, processed_video_path)
            
            # In a real implementation, you would run your AI models here
            # and generate the processed video with detections
            
            processing_results = {
                'total_detections': 0,
                'potholes': 0,
                'cracks': 0,
                'kerbs': 0,
                'processing_time': 0,
                'frames_processed': frame_count
            }
            
        except Exception as e:
            logger.error(f"Error during video processing: {str(e)}")
            # Continue with upload even if processing fails
            processing_results = {'error': str(e)}
        
        # Upload processed video to S3
        update_job_status(job_id, 'PROCESSING', 'Uploading processed video to S3...', 80)
        
        if os.path.exists(processed_video_path):
            with open(processed_video_path, 'rb') as f:
                processed_video_data = f.read()
            
            processed_s3_key = f"{role}/{username}/videos/processed/{upload_id}_{original_filename}"
            
            processed_upload_success, processed_s3_result = s3_manager.upload_image_to_s3(
                processed_video_data, processed_s3_key, content_type='video/mp4'
            )
            
            if processed_upload_success:
                processed_s3_url = processed_s3_result
            else:
                logger.error(f"Failed to upload processed video to S3: {processed_s3_result}")
                processed_s3_url = None
        else:
            processed_s3_url = None
        
        # Update final job status
        db.video_processing_jobs.update_one(
            {'_id': job_doc_id},
            {
                '$set': {
                    'status': 'COMPLETED',
                    'progress': 100,
                    'message': 'Video processing completed successfully',
                    'processed_video_s3_url': processed_s3_url,
                    'results': processing_results,
                    'completed_at': datetime.utcnow(),
                    'updated_at': datetime.utcnow()
                }
            }
        )
        
        # Clean up temporary files
        cleanup_temp_files.delay(video_path, processed_video_path)
        
        update_job_status(job_id, 'SUCCESS', 'Video processing completed successfully', 100)
        
        return {
            'status': 'SUCCESS',
            'upload_id': upload_id,
            'original_s3_url': original_s3_url,
            'processed_s3_url': processed_s3_url,
            'results': processing_results
        }
        
    except Exception as e:
        error_msg = f"Video processing failed: {str(e)}"
        logger.error(f"Task {job_id} failed: {error_msg}")
        logger.error(traceback.format_exc())
        
        # Update job status to failed
        update_job_status(job_id, 'FAILURE', error_msg, None)
        
        # Update database record if it exists
        try:
            db = connect_to_db()
            if db:
                db.video_processing_jobs.update_one(
                    {'job_id': job_id},
                    {
                        '$set': {
                            'status': 'FAILED',
                            'error': error_msg,
                            'updated_at': datetime.utcnow()
                        }
                    }
                )
        except:
            pass
        
        # Re-raise the exception so Celery marks the task as failed
        raise


@celery.task(name='tasks.video_processing.cleanup_temp_files')
def cleanup_temp_files(video_path: str, processed_video_path: str = None):
    """Clean up temporary video files"""
    try:
        if os.path.exists(video_path):
            os.remove(video_path)
            logger.info(f"Cleaned up temporary video file: {video_path}")
        
        if processed_video_path and os.path.exists(processed_video_path):
            os.remove(processed_video_path)
            logger.info(f"Cleaned up processed video file: {processed_video_path}")
            
    except Exception as e:
        logger.error(f"Error cleaning up temporary files: {str(e)}")


@celery.task(name='tasks.video_processing.cleanup_old_uploads')
def cleanup_old_uploads():
    """Periodic task to clean up old upload sessions and files"""
    try:
        upload_manager = VideoUploadManager()
        upload_manager.cleanup_expired_sessions()
        
        # Clean up old video processing jobs (older than 7 days)
        db = connect_to_db()
        if db:
            cutoff_date = datetime.utcnow() - timedelta(days=7)
            old_jobs = db.video_processing_jobs.find({
                'created_at': {'$lt': cutoff_date},
                'status': {'$in': ['COMPLETED', 'FAILED']}
            })
            
            for job in old_jobs:
                # Clean up any remaining files
                if job.get('video_path') and os.path.exists(job['video_path']):
                    os.remove(job['video_path'])
                
                # Remove job record
                db.video_processing_jobs.delete_one({'_id': job['_id']})
                
            logger.info("Cleaned up old video processing jobs")
            
    except Exception as e:
        logger.error(f"Error in cleanup task: {str(e)}")


def update_job_status(job_id: str, status: str, message: str, progress: int = None):
    """Update job status in Celery and optionally in database"""
    try:
        # Update Celery task state
        if current_task:
            meta = {'message': message}
            if progress is not None:
                meta['progress'] = progress
            current_task.update_state(state=status, meta=meta)
        
        # Update database record
        db = connect_to_db()
        if db:
            update_data = {
                'status': status,
                'message': message,
                'updated_at': datetime.utcnow()
            }
            if progress is not None:
                update_data['progress'] = progress
                
            db.video_processing_jobs.update_one(
                {'job_id': job_id},
                {'$set': update_data}
            )
            
    except Exception as e:
        logger.error(f"Error updating job status: {str(e)}")
