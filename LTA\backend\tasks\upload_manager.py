"""
Upload Management Tasks
Celery tasks for handling upload session management and finalization
"""
import os
import logging
from datetime import datetime
from celery import current_task
from celery_app import celery
from utils.video_upload_manager import VideoUploadManager
from tasks.video_processing import process_video_task

logger = logging.getLogger(__name__)

@celery.task(bind=True, name='tasks.upload_manager.finalize_multipart_upload')
def finalize_multipart_upload(self, upload_id: str, session_id: str, username: str, 
                             role: str, coordinates: str = None, selected_model: str = 'All'):
    """
    Finalize multipart upload and trigger video processing
    
    Args:
        upload_id: Unique upload identifier
        session_id: Upload session ID
        username: User who uploaded the video
        role: User role
        coordinates: GPS coordinates
        selected_model: Model type for processing
    """
    job_id = self.request.id
    logger.info(f"Starting upload finalization for {upload_id}")
    
    try:
        # Initialize upload manager
        upload_manager = VideoUploadManager()
        
        # Complete the upload
        result = upload_manager.complete_upload(upload_id)
        
        if result['status'] != 'completed':
            raise Exception(f"Upload completion failed: {result}")
        
        logger.info(f"Upload finalized successfully: {upload_id}")
        
        # Trigger video processing task
        processing_task = process_video_task.delay(
            upload_id=upload_id,
            session_id=session_id,
            username=username,
            role=role,
            coordinates=coordinates,
            selected_model=selected_model
        )
        
        logger.info(f"Triggered video processing task: {processing_task.id}")
        
        return {
            'status': 'success',
            'upload_id': upload_id,
            's3_key': result['s3_key'],
            'processing_task_id': processing_task.id,
            'message': 'Upload finalized and processing started'
        }
        
    except Exception as e:
        logger.error(f"Upload finalization failed for {upload_id}: {str(e)}")
        
        # Try to abort the upload session
        try:
            upload_manager = VideoUploadManager()
            upload_manager.abort_upload(upload_id)
        except:
            pass
        
        raise Exception(f"Upload finalization failed: {str(e)}")

@celery.task(name='tasks.upload_manager.cleanup_expired_sessions')
def cleanup_expired_sessions():
    """
    Periodic task to clean up expired upload sessions
    """
    try:
        logger.info("Starting cleanup of expired upload sessions")
        
        upload_manager = VideoUploadManager()
        upload_manager.cleanup_expired_sessions()
        
        logger.info("Completed cleanup of expired upload sessions")
        return {'status': 'success', 'message': 'Cleanup completed'}
        
    except Exception as e:
        logger.error(f"Failed to cleanup expired sessions: {str(e)}")
        raise Exception(f"Cleanup failed: {str(e)}")

@celery.task(bind=True, name='tasks.upload_manager.abort_upload_session')
def abort_upload_session(self, upload_id: str):
    """
    Abort an upload session
    
    Args:
        upload_id: Upload session ID to abort
    """
    try:
        logger.info(f"Aborting upload session: {upload_id}")
        
        upload_manager = VideoUploadManager()
        result = upload_manager.abort_upload(upload_id)
        
        logger.info(f"Upload session aborted: {upload_id}")
        return result
        
    except Exception as e:
        logger.error(f"Failed to abort upload session {upload_id}: {str(e)}")
        raise Exception(f"Abort failed: {str(e)}")

@celery.task(bind=True, name='tasks.upload_manager.get_upload_progress')
def get_upload_progress(self, upload_id: str):
    """
    Get upload progress for a session
    
    Args:
        upload_id: Upload session ID
        
    Returns:
        Dict containing upload progress information
    """
    try:
        upload_manager = VideoUploadManager()
        status = upload_manager.get_upload_status(upload_id)
        
        return status
        
    except Exception as e:
        logger.error(f"Failed to get upload progress for {upload_id}: {str(e)}")
        return {'status': 'error', 'error': str(e)}

@celery.task(bind=True, name='tasks.upload_manager.validate_upload_integrity')
def validate_upload_integrity(self, upload_id: str):
    """
    Validate upload integrity by checking all chunks
    
    Args:
        upload_id: Upload session ID
        
    Returns:
        Dict containing validation result
    """
    try:
        logger.info(f"Validating upload integrity for: {upload_id}")
        
        upload_manager = VideoUploadManager()
        session = upload_manager._get_upload_session(upload_id)
        
        if not session:
            return {'status': 'error', 'error': 'Upload session not found'}
        
        # Check if all chunks are present
        expected_chunks = set(range(1, session['chunk_count'] + 1))
        uploaded_chunks = set(session['uploaded_chunks'])
        
        if expected_chunks == uploaded_chunks:
            logger.info(f"Upload integrity validated for: {upload_id}")
            return {
                'status': 'valid',
                'upload_id': upload_id,
                'total_chunks': session['chunk_count'],
                'uploaded_chunks': len(uploaded_chunks)
            }
        else:
            missing_chunks = expected_chunks - uploaded_chunks
            logger.warning(f"Upload integrity check failed for {upload_id}: missing chunks {list(missing_chunks)}")
            return {
                'status': 'invalid',
                'upload_id': upload_id,
                'missing_chunks': list(missing_chunks),
                'total_chunks': session['chunk_count'],
                'uploaded_chunks': len(uploaded_chunks)
            }
        
    except Exception as e:
        logger.error(f"Failed to validate upload integrity for {upload_id}: {str(e)}")
        return {'status': 'error', 'error': str(e)}
