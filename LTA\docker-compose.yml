version: '3.8'

services:
  # MongoDB service
  mongo:
    image: mongo:6
    container_name: lta-mongodb
    restart: always
    ports:
      - "27017:27017"
    volumes:
      - mongo-data:/data/db
    networks:
      - lta-network
    healthcheck:
      test: ["C<PERSON>", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 40s

  # Redis service for Celery broker
  redis:
    image: redis:7-alpine
    container_name: lta-redis
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - lta-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru

  # Backend Flask service
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: lta-backend
    restart: always
    ports:
      - "5000:5000"
    depends_on:
      mongo:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./map_frames:/app/map_frames
      - ./backend/temp_uploads:/app/temp_uploads
    networks:
      - lta-network
    environment:
      - MONGO_URI=mongodb://mongo:27017
      - DB_NAME=LTA
      - FLASK_ENV=production
      - PYTHONUNBUFFERED=1
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0

  # Celery Worker service
  celery-worker:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: lta-celery-worker
    restart: always
    depends_on:
      mongo:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./map_frames:/app/map_frames
      - ./backend/temp_uploads:/app/temp_uploads
    networks:
      - lta-network
    environment:
      - MONGO_URI=mongodb://mongo:27017
      - DB_NAME=LTA
      - PYTHONUNBUFFERED=1
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    command: celery -A celery_app worker --loglevel=info --concurrency=2

  # Celery Beat service (for periodic tasks)
  celery-beat:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: lta-celery-beat
    restart: always
    depends_on:
      mongo:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./map_frames:/app/map_frames
      - ./backend/temp_uploads:/app/temp_uploads
    networks:
      - lta-network
    environment:
      - MONGO_URI=mongodb://mongo:27017
      - DB_NAME=LTA
      - PYTHONUNBUFFERED=1
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    command: celery -A celery_app beat --loglevel=info

  # Frontend React service
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: lta-frontend
    restart: always
    ports:
      - "80:80"
    depends_on:
      - backend
    networks:
      - lta-network

networks:
  lta-network:
    driver: bridge

volumes:
  mongo-data:
    driver: local
  redis-data:
    driver: local