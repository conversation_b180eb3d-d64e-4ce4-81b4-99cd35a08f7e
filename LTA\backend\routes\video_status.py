"""
Video Processing Status API
Endpoints for checking video processing status and progress
"""
import logging
from flask import Blueprint, request, jsonify
from flask_socketio import So<PERSON><PERSON>, emit, join_room, leave_room
from utils.auth_middleware import token_required
from config.db import connect_to_db
from tasks.video_processing import get_job_status
from celery_app import celery
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

# Create blueprint
video_status_bp = Blueprint('video_status', __name__)

# SocketIO instance (will be initialized by main app)
socketio = None

def init_socketio(app_socketio):
    """Initialize SocketIO instance"""
    global socketio
    socketio = app_socketio

@video_status_bp.route('/job/<job_id>', methods=['GET'])
@token_required
def get_processing_status(job_id):
    """
    Get processing job status by job ID
    
    Args:
        job_id: Celery task ID
    """
    try:
        # Get job status from Celery
        task = celery.AsyncResult(job_id)
        
        # Get detailed status from database
        status_info = get_job_status(job_id)
        
        # Combine Celery task state with database info
        result = {
            'job_id': job_id,
            'celery_state': task.state,
            'celery_info': task.info if task.info else {},
            'database_status': status_info,
            'is_ready': task.ready(),
            'is_successful': task.successful() if task.ready() else False,
            'is_failed': task.failed() if task.ready() else False
        }
        
        # Normalize status
        if status_info.get('status') in ['COMPLETED', 'FAILED']:
            result['final_status'] = status_info['status']
        elif task.state == 'SUCCESS':
            result['final_status'] = 'COMPLETED'
        elif task.state == 'FAILURE':
            result['final_status'] = 'FAILED'
        elif task.state in ['PENDING', 'RETRY']:
            result['final_status'] = 'QUEUED'
        else:
            result['final_status'] = 'PROCESSING'
        
        return jsonify({
            'success': True,
            'status': result
        })
        
    except Exception as e:
        logger.error(f"Failed to get processing status: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Failed to get processing status: {str(e)}'
        }), 500

@video_status_bp.route('/upload/<upload_id>', methods=['GET'])
@token_required
def get_upload_processing_status(upload_id):
    """
    Get processing status by upload ID
    
    Args:
        upload_id: Upload session ID
    """
    try:
        db = connect_to_db()
        if not db:
            return jsonify({
                'success': False,
                'error': 'Database connection failed'
            }), 500
        
        # Find processing job by upload_id
        job = db.video_processing_jobs.find_one(
            {'upload_id': upload_id},
            sort=[('created_at', -1)]  # Get most recent job
        )
        
        if not job:
            return jsonify({
                'success': False,
                'error': 'Processing job not found for upload'
            }), 404
        
        job_id = job['job_id']
        
        # Get detailed status
        return get_processing_status(job_id)
        
    except Exception as e:
        logger.error(f"Failed to get upload processing status: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Failed to get upload processing status: {str(e)}'
        }), 500

@video_status_bp.route('/user/jobs', methods=['GET'])
@token_required
def get_user_jobs():
    """
    Get all processing jobs for the current user
    """
    try:
        username = request.user.get('username')
        if not username:
            return jsonify({
                'success': False,
                'error': 'User not authenticated'
            }), 401
        
        db = connect_to_db()
        if not db:
            return jsonify({
                'success': False,
                'error': 'Database connection failed'
            }), 500
        
        # Get query parameters
        limit = int(request.args.get('limit', 50))
        offset = int(request.args.get('offset', 0))
        status_filter = request.args.get('status')  # Optional status filter
        
        # Build query
        query = {'username': username}
        if status_filter:
            query['status'] = status_filter.upper()
        
        # Get jobs
        jobs = list(db.video_processing_jobs.find(
            query,
            sort=[('created_at', -1)]
        ).skip(offset).limit(limit))
        
        # Get total count
        total_count = db.video_processing_jobs.count_documents(query)
        
        # Format jobs for response
        formatted_jobs = []
        for job in jobs:
            formatted_job = {
                'job_id': job['job_id'],
                'upload_id': job['upload_id'],
                'video_id': job.get('video_id'),
                'filename': job.get('original_filename'),
                'status': job.get('status'),
                'progress': job.get('progress', 0),
                'message': job.get('message', ''),
                'selected_model': job.get('selected_model'),
                'file_size': job.get('file_size'),
                'created_at': job.get('created_at'),
                'updated_at': job.get('updated_at'),
                'processing_started_at': job.get('processing_started_at'),
                'processing_completed_at': job.get('processing_completed_at'),
                'error': job.get('error')
            }
            formatted_jobs.append(formatted_job)
        
        return jsonify({
            'success': True,
            'jobs': formatted_jobs,
            'total_count': total_count,
            'limit': limit,
            'offset': offset
        })
        
    except Exception as e:
        logger.error(f"Failed to get user jobs: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Failed to get user jobs: {str(e)}'
        }), 500

@video_status_bp.route('/stats', methods=['GET'])
@token_required
def get_processing_stats():
    """
    Get processing statistics for the current user
    """
    try:
        username = request.user.get('username')
        if not username:
            return jsonify({
                'success': False,
                'error': 'User not authenticated'
            }), 401
        
        db = connect_to_db()
        if not db:
            return jsonify({
                'success': False,
                'error': 'Database connection failed'
            }), 500
        
        # Get statistics
        pipeline = [
            {'$match': {'username': username}},
            {'$group': {
                '_id': '$status',
                'count': {'$sum': 1}
            }}
        ]
        
        status_counts = list(db.video_processing_jobs.aggregate(pipeline))
        
        # Format statistics
        stats = {
            'total_jobs': 0,
            'completed': 0,
            'processing': 0,
            'queued': 0,
            'failed': 0
        }
        
        for stat in status_counts:
            status = stat['_id'].lower() if stat['_id'] else 'unknown'
            count = stat['count']
            stats['total_jobs'] += count
            
            if status == 'completed':
                stats['completed'] = count
            elif status == 'processing':
                stats['processing'] = count
            elif status == 'queued':
                stats['queued'] = count
            elif status == 'failed':
                stats['failed'] = count
        
        # Get recent activity (last 24 hours)
        recent_cutoff = datetime.utcnow() - timedelta(hours=24)
        recent_jobs = db.video_processing_jobs.count_documents({
            'username': username,
            'created_at': {'$gte': recent_cutoff}
        })
        
        stats['recent_jobs_24h'] = recent_jobs
        
        return jsonify({
            'success': True,
            'stats': stats
        })
        
    except Exception as e:
        logger.error(f"Failed to get processing stats: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Failed to get processing stats: {str(e)}'
        }), 500

# WebSocket event handlers (if SocketIO is available)
def setup_websocket_handlers():
    """Setup WebSocket event handlers for real-time status updates"""
    if not socketio:
        return
    
    @socketio.on('join_job_room')
    def on_join_job_room(data):
        """Join a room for job status updates"""
        try:
            job_id = data.get('job_id')
            if job_id:
                join_room(f"job_{job_id}")
                emit('joined_room', {'job_id': job_id, 'room': f"job_{job_id}"})
                logger.info(f"Client joined job room: job_{job_id}")
        except Exception as e:
            logger.error(f"Failed to join job room: {e}")
            emit('error', {'message': 'Failed to join job room'})
    
    @socketio.on('leave_job_room')
    def on_leave_job_room(data):
        """Leave a job status room"""
        try:
            job_id = data.get('job_id')
            if job_id:
                leave_room(f"job_{job_id}")
                emit('left_room', {'job_id': job_id, 'room': f"job_{job_id}"})
                logger.info(f"Client left job room: job_{job_id}")
        except Exception as e:
            logger.error(f"Failed to leave job room: {e}")
            emit('error', {'message': 'Failed to leave job room'})
    
    @socketio.on('get_job_status')
    def on_get_job_status(data):
        """Get current job status via WebSocket"""
        try:
            job_id = data.get('job_id')
            if job_id:
                status_info = get_job_status(job_id)
                emit('job_status_update', {
                    'job_id': job_id,
                    'status': status_info
                })
        except Exception as e:
            logger.error(f"Failed to get job status via WebSocket: {e}")
            emit('error', {'message': 'Failed to get job status'})

def broadcast_job_update(job_id: str, status_data: dict):
    """
    Broadcast job status update to all clients in the job room
    
    Args:
        job_id: Job ID
        status_data: Status data to broadcast
    """
    if socketio:
        try:
            socketio.emit('job_status_update', {
                'job_id': job_id,
                'status': status_data
            }, room=f"job_{job_id}")
            logger.info(f"Broadcasted status update for job {job_id}")
        except Exception as e:
            logger.error(f"Failed to broadcast job update: {e}")

# Initialize WebSocket handlers when module is imported
setup_websocket_handlers()
