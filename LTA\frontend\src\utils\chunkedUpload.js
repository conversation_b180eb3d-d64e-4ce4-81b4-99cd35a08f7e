/**
 * Chunked Upload Utility
 * Handles large file uploads with resumable/chunked upload support
 */

class ChunkedUploader {
  constructor(options = {}) {
    this.baseUrl = options.baseUrl || '/api/upload';
    this.chunkSize = options.chunkSize || 10 * 1024 * 1024; // 10MB default
    this.maxRetries = options.maxRetries || 3;
    this.retryDelay = options.retryDelay || 1000; // 1 second
    this.onProgress = options.onProgress || (() => {});
    this.onStatusChange = options.onStatusChange || (() => {});
    this.onError = options.onError || (() => {});
    this.onComplete = options.onComplete || (() => {});
    
    this.uploadId = null;
    this.sessionId = null;
    this.file = null;
    this.totalChunks = 0;
    this.uploadedChunks = new Set();
    this.isUploading = false;
    this.isPaused = false;
    this.isAborted = false;
  }

  /**
   * Initialize upload session
   */
  async initialize(file, metadata = {}) {
    try {
      this.file = file;
      this.totalChunks = Math.ceil(file.size / this.chunkSize);
      
      this.onStatusChange('initializing', 'Initializing upload...');
      
      const response = await fetch(`${this.baseUrl}/initialize`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          filename: file.name,
          file_size: file.size,
          content_type: file.type,
          coordinates: metadata.coordinates,
          selected_model: metadata.selectedModel || 'All'
        })
      });

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to initialize upload');
      }

      this.uploadId = result.upload_id;
      this.sessionId = result.session_id;
      this.totalChunks = result.chunk_count;
      
      this.onStatusChange('initialized', 'Upload initialized successfully');
      
      return {
        uploadId: this.uploadId,
        sessionId: this.sessionId,
        chunkCount: this.totalChunks,
        useMultipart: result.use_multipart
      };
      
    } catch (error) {
      this.onError('initialization_failed', error.message);
      throw error;
    }
  }

  /**
   * Start or resume upload
   */
  async start() {
    if (!this.uploadId || !this.file) {
      throw new Error('Upload not initialized');
    }

    this.isUploading = true;
    this.isPaused = false;
    this.isAborted = false;
    
    this.onStatusChange('uploading', 'Starting upload...');

    try {
      // Upload chunks sequentially or in parallel
      await this.uploadChunks();
      
      if (!this.isAborted && !this.isPaused) {
        // Complete upload
        await this.complete();
      }
      
    } catch (error) {
      this.isUploading = false;
      this.onError('upload_failed', error.message);
      throw error;
    }
  }

  /**
   * Upload all chunks
   */
  async uploadChunks() {
    const concurrency = 3; // Upload 3 chunks in parallel
    const chunks = [];
    
    // Create chunk upload tasks
    for (let i = 1; i <= this.totalChunks; i++) {
      if (!this.uploadedChunks.has(i)) {
        chunks.push(i);
      }
    }

    // Upload chunks with limited concurrency
    while (chunks.length > 0 && !this.isAborted && !this.isPaused) {
      const batch = chunks.splice(0, concurrency);
      const promises = batch.map(chunkNumber => this.uploadChunk(chunkNumber));
      
      await Promise.all(promises);
      
      // Update progress
      const progress = (this.uploadedChunks.size / this.totalChunks) * 100;
      this.onProgress(progress, this.uploadedChunks.size, this.totalChunks);
    }
  }

  /**
   * Upload a single chunk
   */
  async uploadChunk(chunkNumber) {
    if (this.uploadedChunks.has(chunkNumber)) {
      return; // Already uploaded
    }

    const start = (chunkNumber - 1) * this.chunkSize;
    const end = Math.min(start + this.chunkSize, this.file.size);
    const chunk = this.file.slice(start, end);

    let retries = 0;
    
    while (retries <= this.maxRetries) {
      try {
        const formData = new FormData();
        formData.append('upload_id', this.uploadId);
        formData.append('chunk_number', chunkNumber.toString());
        formData.append('chunk', chunk);

        const response = await fetch(`${this.baseUrl}/chunk`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          },
          body: formData
        });

        const result = await response.json();
        
        if (!result.success) {
          throw new Error(result.error || 'Failed to upload chunk');
        }

        this.uploadedChunks.add(chunkNumber);
        
        // Update progress
        const progress = (this.uploadedChunks.size / this.totalChunks) * 100;
        this.onProgress(progress, this.uploadedChunks.size, this.totalChunks);
        
        return result;
        
      } catch (error) {
        retries++;
        
        if (retries > this.maxRetries) {
          throw new Error(`Failed to upload chunk ${chunkNumber} after ${this.maxRetries} retries: ${error.message}`);
        }
        
        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, this.retryDelay * retries));
      }
    }
  }

  /**
   * Complete the upload
   */
  async complete() {
    try {
      this.onStatusChange('completing', 'Completing upload...');
      
      const response = await fetch(`${this.baseUrl}/complete`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          upload_id: this.uploadId,
          session_id: this.sessionId
        })
      });

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to complete upload');
      }

      this.isUploading = false;
      this.onStatusChange('completed', 'Upload completed successfully');
      this.onComplete(result);
      
      return result;
      
    } catch (error) {
      this.onError('completion_failed', error.message);
      throw error;
    }
  }

  /**
   * Pause upload
   */
  pause() {
    this.isPaused = true;
    this.isUploading = false;
    this.onStatusChange('paused', 'Upload paused');
  }

  /**
   * Resume upload
   */
  async resume() {
    if (this.isAborted) {
      throw new Error('Cannot resume aborted upload');
    }
    
    this.isPaused = false;
    await this.start();
  }

  /**
   * Abort upload
   */
  async abort() {
    try {
      this.isAborted = true;
      this.isUploading = false;
      this.isPaused = false;
      
      if (this.uploadId) {
        const response = await fetch(`${this.baseUrl}/abort`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          },
          body: JSON.stringify({
            upload_id: this.uploadId
          })
        });

        const result = await response.json();
        
        if (!result.success) {
          console.warn('Failed to abort upload on server:', result.error);
        }
      }
      
      this.onStatusChange('aborted', 'Upload aborted');
      
    } catch (error) {
      console.error('Error aborting upload:', error);
      this.onStatusChange('aborted', 'Upload aborted (with errors)');
    }
  }

  /**
   * Get upload status
   */
  async getStatus() {
    if (!this.uploadId) {
      return null;
    }

    try {
      const response = await fetch(`${this.baseUrl}/status/${this.uploadId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to get upload status');
      }

      return result.upload_status;
      
    } catch (error) {
      console.error('Error getting upload status:', error);
      return null;
    }
  }

  /**
   * Get upload progress
   */
  getProgress() {
    if (this.totalChunks === 0) {
      return 0;
    }
    
    return (this.uploadedChunks.size / this.totalChunks) * 100;
  }

  /**
   * Check if upload is complete
   */
  isComplete() {
    return this.uploadedChunks.size === this.totalChunks;
  }
}

export default ChunkedUploader;
