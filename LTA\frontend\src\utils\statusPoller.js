/**
 * Status Polling Utility
 * Handles polling for video processing status updates
 */

class StatusPoller {
  constructor(options = {}) {
    this.baseUrl = options.baseUrl || '/api/status';
    this.pollInterval = options.pollInterval || 2000; // 2 seconds default
    this.maxPollTime = options.maxPollTime || 7200000; // 2 hours default
    this.onStatusUpdate = options.onStatusUpdate || (() => {});
    this.onComplete = options.onComplete || (() => {});
    this.onError = options.onError || (() => {});
    
    this.isPolling = false;
    this.pollTimer = null;
    this.startTime = null;
    this.jobId = null;
    this.uploadId = null;
  }

  /**
   * Start polling for job status by job ID
   */
  startPollingByJobId(jobId) {
    this.jobId = jobId;
    this.uploadId = null;
    this._startPolling(`/job/${jobId}`);
  }

  /**
   * Start polling for job status by upload ID
   */
  startPollingByUploadId(uploadId) {
    this.uploadId = uploadId;
    this.jobId = null;
    this._startPolling(`/upload/${uploadId}`);
  }

  /**
   * Internal method to start polling
   */
  _startPolling(endpoint) {
    if (this.isPolling) {
      this.stop();
    }

    this.isPolling = true;
    this.startTime = Date.now();
    
    this._poll(endpoint);
  }

  /**
   * Poll for status updates
   */
  async _poll(endpoint) {
    if (!this.isPolling) {
      return;
    }

    // Check if we've exceeded max poll time
    if (Date.now() - this.startTime > this.maxPollTime) {
      this.stop();
      this.onError('timeout', 'Polling timeout exceeded');
      return;
    }

    try {
      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to get status');
      }

      const status = result.status;
      this.onStatusUpdate(status);

      // Check if processing is complete
      const finalStatus = status.final_status || status.database_status?.status;
      
      if (finalStatus === 'COMPLETED') {
        this.stop();
        this.onComplete(status);
        return;
      } else if (finalStatus === 'FAILED') {
        this.stop();
        this.onError('processing_failed', status.database_status?.error || 'Processing failed');
        return;
      }

      // Schedule next poll
      this.pollTimer = setTimeout(() => {
        this._poll(endpoint);
      }, this.pollInterval);

    } catch (error) {
      console.error('Error polling status:', error);
      
      // Continue polling on network errors, but stop on auth errors
      if (error.message.includes('401') || error.message.includes('403')) {
        this.stop();
        this.onError('auth_error', 'Authentication error');
        return;
      }

      // Schedule next poll for network errors
      this.pollTimer = setTimeout(() => {
        this._poll(endpoint);
      }, this.pollInterval * 2); // Double the interval on errors
    }
  }

  /**
   * Stop polling
   */
  stop() {
    this.isPolling = false;
    
    if (this.pollTimer) {
      clearTimeout(this.pollTimer);
      this.pollTimer = null;
    }
  }

  /**
   * Get current polling status
   */
  getPollingInfo() {
    return {
      isPolling: this.isPolling,
      jobId: this.jobId,
      uploadId: this.uploadId,
      startTime: this.startTime,
      elapsedTime: this.startTime ? Date.now() - this.startTime : 0
    };
  }
}

/**
 * Processing Status Manager
 * Manages multiple status pollers and provides a unified interface
 */
class ProcessingStatusManager {
  constructor() {
    this.pollers = new Map();
    this.listeners = new Map();
  }

  /**
   * Start monitoring a job
   */
  monitorJob(jobId, callbacks = {}) {
    // Stop existing poller for this job
    if (this.pollers.has(jobId)) {
      this.pollers.get(jobId).stop();
    }

    const poller = new StatusPoller({
      onStatusUpdate: (status) => {
        this._notifyListeners(jobId, 'status_update', status);
        if (callbacks.onStatusUpdate) {
          callbacks.onStatusUpdate(status);
        }
      },
      onComplete: (status) => {
        this._notifyListeners(jobId, 'complete', status);
        if (callbacks.onComplete) {
          callbacks.onComplete(status);
        }
        this.pollers.delete(jobId);
      },
      onError: (type, message) => {
        this._notifyListeners(jobId, 'error', { type, message });
        if (callbacks.onError) {
          callbacks.onError(type, message);
        }
        this.pollers.delete(jobId);
      }
    });

    this.pollers.set(jobId, poller);
    poller.startPollingByJobId(jobId);
    
    return poller;
  }

  /**
   * Start monitoring by upload ID
   */
  monitorUpload(uploadId, callbacks = {}) {
    const key = `upload_${uploadId}`;
    
    // Stop existing poller for this upload
    if (this.pollers.has(key)) {
      this.pollers.get(key).stop();
    }

    const poller = new StatusPoller({
      onStatusUpdate: (status) => {
        this._notifyListeners(key, 'status_update', status);
        if (callbacks.onStatusUpdate) {
          callbacks.onStatusUpdate(status);
        }
      },
      onComplete: (status) => {
        this._notifyListeners(key, 'complete', status);
        if (callbacks.onComplete) {
          callbacks.onComplete(status);
        }
        this.pollers.delete(key);
      },
      onError: (type, message) => {
        this._notifyListeners(key, 'error', { type, message });
        if (callbacks.onError) {
          callbacks.onError(type, message);
        }
        this.pollers.delete(key);
      }
    });

    this.pollers.set(key, poller);
    poller.startPollingByUploadId(uploadId);
    
    return poller;
  }

  /**
   * Stop monitoring a job
   */
  stopMonitoring(identifier) {
    const keys = [identifier, `upload_${identifier}`];
    
    for (const key of keys) {
      if (this.pollers.has(key)) {
        this.pollers.get(key).stop();
        this.pollers.delete(key);
      }
    }
  }

  /**
   * Stop all monitoring
   */
  stopAll() {
    for (const poller of this.pollers.values()) {
      poller.stop();
    }
    this.pollers.clear();
  }

  /**
   * Add event listener
   */
  addEventListener(identifier, callback) {
    if (!this.listeners.has(identifier)) {
      this.listeners.set(identifier, []);
    }
    this.listeners.get(identifier).push(callback);
  }

  /**
   * Remove event listener
   */
  removeEventListener(identifier, callback) {
    if (this.listeners.has(identifier)) {
      const callbacks = this.listeners.get(identifier);
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  /**
   * Notify listeners
   */
  _notifyListeners(identifier, event, data) {
    if (this.listeners.has(identifier)) {
      const callbacks = this.listeners.get(identifier);
      callbacks.forEach(callback => {
        try {
          callback(event, data);
        } catch (error) {
          console.error('Error in status listener:', error);
        }
      });
    }
  }

  /**
   * Get all active pollers
   */
  getActivePollers() {
    const active = [];
    for (const [key, poller] of this.pollers.entries()) {
      if (poller.isPolling) {
        active.push({
          key,
          ...poller.getPollingInfo()
        });
      }
    }
    return active;
  }
}

// Export singleton instance
export const statusManager = new ProcessingStatusManager();
export { StatusPoller, ProcessingStatusManager };
