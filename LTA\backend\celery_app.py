"""
Celery Application Configuration
Production-ready Celery setup for video processing tasks
"""
import os
import logging
from celery import Celery
from config.upload_config import CELERY_BROKER_URL, CELERY_RESULT_BACKEND, JOB_TIMEOUT, JOB_RETRY_LIMIT

# Configure logging
logger = logging.getLogger(__name__)

# Create Celery application
celery = Celery('video_processing_app')

# Celery configuration
celery.conf.update(
    # Broker settings
    broker_url=CELERY_BROKER_URL,
    result_backend=CELERY_RESULT_BACKEND,
    
    # Task settings
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,
    
    # Task execution settings
    task_track_started=True,
    task_time_limit=JOB_TIMEOUT,
    task_soft_time_limit=JOB_TIMEOUT - 300,  # 5 minutes before hard limit
    worker_prefetch_multiplier=1,
    task_acks_late=True,
    worker_disable_rate_limits=False,
    
    # Result backend settings
    result_expires=3600,  # 1 hour
    result_persistent=True,
    
    # Task routing
    task_routes={
        'tasks.video_processing.process_video_task': {'queue': 'video_processing'},
        'tasks.video_processing.cleanup_old_uploads': {'queue': 'maintenance'},
        'tasks.upload_manager.cleanup_expired_sessions': {'queue': 'maintenance'},
        'tasks.upload_manager.finalize_multipart_upload': {'queue': 'upload_finalization'},
    },
    
    # Worker settings
    worker_max_tasks_per_child=50,
    worker_max_memory_per_child=2000000,  # 2GB in KB
    
    # Beat schedule for periodic tasks
    beat_schedule={
        'cleanup-old-uploads': {
            'task': 'tasks.video_processing.cleanup_old_uploads',
            'schedule': 3600.0,  # Run every hour
        },
        'cleanup-expired-sessions': {
            'task': 'tasks.upload_manager.cleanup_expired_sessions',
            'schedule': 1800.0,  # Run every 30 minutes
        },
    },
    beat_schedule_filename='celerybeat-schedule',
)

# Auto-discover tasks
celery.autodiscover_tasks(['tasks'])

# Task retry configuration
@celery.task(bind=True)
def debug_task(self):
    """Debug task for testing Celery setup"""
    print(f'Request: {self.request!r}')
    return 'Celery is working!'

# Error handling
class CallbackTask(celery.Task):
    """Base task class with error handling"""
    
    def on_failure(self, exc, task_id, args, kwargs, einfo):
        """Handle task failure"""
        logger.error(f'Task {task_id} failed: {exc}')
        logger.error(f'Exception info: {einfo}')
        
        # Update job status in database if applicable
        try:
            from tasks.video_processing import update_job_status
            if len(args) > 0:
                # Assume first argument might be a job/upload ID
                update_job_status(task_id, 'FAILED', f'Task failed: {str(exc)}', 0)
        except Exception as e:
            logger.error(f'Failed to update job status: {e}')
    
    def on_success(self, retval, task_id, args, kwargs):
        """Handle task success"""
        logger.info(f'Task {task_id} completed successfully')
    
    def on_retry(self, exc, task_id, args, kwargs, einfo):
        """Handle task retry"""
        logger.warning(f'Task {task_id} retrying: {exc}')

# Set default task base class
celery.Task = CallbackTask

if __name__ == '__main__':
    celery.start()
