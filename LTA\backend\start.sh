#!/bin/bash

# Start script for LTA Backend
# This script can be used to start different services based on the SERVICE environment variable

set -e

# Default to Flask app if no service specified
SERVICE=${SERVICE:-app}

echo "Starting LTA Backend Service: $SERVICE"

case $SERVICE in
  "app")
    echo "Starting Flask application..."
    python app.py
    ;;
  "worker")
    echo "Starting Celery worker..."
    celery -A celery_app worker --loglevel=info --concurrency=2
    ;;
  "beat")
    echo "Starting Celery beat scheduler..."
    celery -A celery_app beat --loglevel=info
    ;;
  "flower")
    echo "Starting Celery Flower monitoring..."
    celery -A celery_app flower --port=5555
    ;;
  *)
    echo "Unknown service: $SERVICE"
    echo "Available services: app, worker, beat, flower"
    exit 1
    ;;
esac
