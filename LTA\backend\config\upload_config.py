"""
Upload Configuration Settings
Centralized configuration for file upload limits and settings
"""
import os
from typing import Dict, Any

# File size limits (in bytes)
MAX_IMAGE_SIZE = 50 * 1024 * 1024  # 50MB
MAX_VIDEO_SIZE = 5 * 1024 * 1024 * 1024  # 5GB

# Video duration limits (in seconds)
MAX_VIDEO_DURATION = 3600  # 1 hour

# Chunked upload settings
CHUNK_SIZE = 10 * 1024 * 1024  # 10MB per chunk
MIN_MULTIPART_SIZE = 100 * 1024 * 1024  # 100MB minimum for multipart upload
MAX_CHUNKS_PER_UPLOAD = 1000  # Maximum number of chunks per upload

# Upload session settings
UPLOAD_SESSION_TIMEOUT = 24 * 3600  # 24 hours in seconds
CLEANUP_INTERVAL = 3600  # 1 hour in seconds

# S3 multipart upload settings
S3_MULTIPART_THRESHOLD = MIN_MULTIPART_SIZE
S3_MULTIPART_CHUNKSIZE = CHUNK_SIZE
S3_MAX_CONCURRENCY = 10
S3_USE_THREADS = True

# Redis settings for job queue
REDIS_URL = os.getenv('REDIS_URL', 'redis://localhost:6379/0')
CELERY_BROKER_URL = os.getenv('CELERY_BROKER_URL', REDIS_URL)
CELERY_RESULT_BACKEND = os.getenv('CELERY_RESULT_BACKEND', REDIS_URL)

# Job processing settings
JOB_TIMEOUT = 7200  # 2 hours in seconds
JOB_RETRY_LIMIT = 3
JOB_RETRY_DELAY = 300  # 5 minutes in seconds

def get_upload_config() -> Dict[str, Any]:
    """
    Get complete upload configuration as dictionary
    
    Returns:
        Dict containing all upload configuration settings
    """
    return {
        'max_image_size': MAX_IMAGE_SIZE,
        'max_video_size': MAX_VIDEO_SIZE,
        'max_video_duration': MAX_VIDEO_DURATION,
        'chunk_size': CHUNK_SIZE,
        'min_multipart_size': MIN_MULTIPART_SIZE,
        'max_chunks_per_upload': MAX_CHUNKS_PER_UPLOAD,
        'upload_session_timeout': UPLOAD_SESSION_TIMEOUT,
        'cleanup_interval': CLEANUP_INTERVAL,
        's3_multipart_threshold': S3_MULTIPART_THRESHOLD,
        's3_multipart_chunksize': S3_MULTIPART_CHUNKSIZE,
        's3_max_concurrency': S3_MAX_CONCURRENCY,
        's3_use_threads': S3_USE_THREADS,
        'redis_url': REDIS_URL,
        'celery_broker_url': CELERY_BROKER_URL,
        'celery_result_backend': CELERY_RESULT_BACKEND,
        'job_timeout': JOB_TIMEOUT,
        'job_retry_limit': JOB_RETRY_LIMIT,
        'job_retry_delay': JOB_RETRY_DELAY
    }

def get_frontend_config() -> Dict[str, Any]:
    """
    Get configuration settings safe to expose to frontend
    
    Returns:
        Dict containing frontend-safe configuration settings
    """
    return {
        'max_image_size': MAX_IMAGE_SIZE,
        'max_video_size': MAX_VIDEO_SIZE,
        'max_video_duration': MAX_VIDEO_DURATION,
        'chunk_size': CHUNK_SIZE,
        'max_chunks_per_upload': MAX_CHUNKS_PER_UPLOAD
    }
