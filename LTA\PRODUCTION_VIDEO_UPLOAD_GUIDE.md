# Production-Ready Video Upload System

This document describes the enhanced video upload system that supports large files (up to 5GB) with chunked/resumable uploads, background processing, and real-time status tracking.

## Features

### 1. Enhanced Upload Limits
- **Video Duration**: Increased from 1 minute to 1 hour (3600 seconds)
- **File Size**: Increased from 500MB to 5GB
- **Supported Formats**: .mp4, .avi, .mov, .mkv, .wmv, .flv, .webm, .m4v, .3gp, .ogv

### 2. Chunked/Resumable Uploads
- **Chunk Size**: 10MB per chunk (configurable)
- **Multipart Upload**: Automatic S3 multipart upload for files > 100MB
- **Resume Support**: Upload can be paused and resumed
- **Error Recovery**: Automatic retry with exponential backoff
- **Progress Tracking**: Real-time upload progress with chunk-level granularity

### 3. Background Processing
- **Celery Integration**: Asynchronous task processing with Redis broker
- **Job Persistence**: Processing continues even if user closes browser
- **Status Tracking**: Real-time status updates (Queued, Processing, Completed, Failed)
- **Error Handling**: Comprehensive error handling with retry mechanisms
- **Resource Management**: Automatic cleanup of temporary files and expired sessions

### 4. User Experience
- **Dual Upload Options**: Traditional upload for small files, chunked upload for large files
- **Real-time Progress**: Upload and processing progress bars
- **Status Polling**: Automatic status updates without page refresh
- **Pause/Resume**: Upload control buttons
- **Error Messages**: Clear, actionable error messages

## Architecture

### Backend Components

1. **Chunked Upload API** (`/api/upload/`)
   - `POST /initialize` - Initialize upload session
   - `POST /chunk` - Upload individual chunks
   - `POST /complete` - Complete upload and start processing
   - `GET /status/<upload_id>` - Get upload status
   - `POST /abort` - Abort upload session

2. **Status API** (`/api/status/`)
   - `GET /job/<job_id>` - Get processing status by job ID
   - `GET /upload/<upload_id>` - Get processing status by upload ID
   - `GET /user/jobs` - Get all user jobs
   - `GET /stats` - Get processing statistics

3. **Background Tasks**
   - `process_video_task` - Main video processing task
   - `finalize_multipart_upload` - Complete chunked upload
   - `cleanup_expired_sessions` - Periodic cleanup task

4. **Core Classes**
   - `VideoUploadManager` - Handles chunked uploads and S3 multipart
   - `ChunkedUploader` (Frontend) - Client-side upload management
   - `StatusPoller` (Frontend) - Status polling and updates

### Database Schema

#### Upload Sessions Collection
```javascript
{
  upload_id: "uuid",
  session_id: "uuid", 
  filename: "video.mp4",
  file_size: 1073741824,
  content_type: "video/mp4",
  username: "user1",
  role: "supervisor",
  coordinates: "lat,lng",
  selected_model: "All",
  chunk_count: 102,
  chunk_size: 10485760,
  uploaded_chunks: [1, 2, 3, ...],
  use_multipart: true,
  s3_key: "role/username/videos/original/upload_id_filename",
  s3_multipart_upload_id: "multipart_id",
  s3_parts: [{PartNumber: 1, ETag: "etag1"}, ...],
  status: "COMPLETED",
  created_at: ISODate(),
  expires_at: ISODate(),
  last_activity: ISODate()
}
```

#### Video Processing Jobs Collection
```javascript
{
  job_id: "celery_task_id",
  upload_id: "uuid",
  session_id: "uuid",
  video_id: "uuid",
  username: "user1", 
  role: "supervisor",
  coordinates: "lat,lng",
  selected_model: "All",
  original_filename: "video.mp4",
  original_video_s3_key: "role/username/videos/original/...",
  processed_video_s3_key: "role/username/videos/processed/...",
  file_size: 1073741824,
  status: "PROCESSING",
  progress: 45,
  message: "Processing video frame 450/1000",
  total_detections: 25,
  total_frames: 1000,
  processed_frames: 450,
  created_at: ISODate(),
  updated_at: ISODate(),
  processing_started_at: ISODate(),
  processing_completed_at: ISODate(),
  error: "error message if failed"
}
```

## Configuration

### Environment Variables

```bash
# Redis Configuration
REDIS_URL=redis://localhost:6379/0
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# AWS Configuration
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_REGION=us-east-1
AWS_FOLDER=bucket/prefix/path

# Upload Configuration
MAX_VIDEO_SIZE=5368709120  # 5GB in bytes
MAX_VIDEO_DURATION=3600    # 1 hour in seconds
CHUNK_SIZE=10485760        # 10MB in bytes
```

### Upload Configuration (`config/upload_config.py`)

```python
# File size limits (in bytes)
MAX_VIDEO_SIZE = 5 * 1024 * 1024 * 1024  # 5GB

# Video duration limits (in seconds)  
MAX_VIDEO_DURATION = 3600  # 1 hour

# Chunked upload settings
CHUNK_SIZE = 10 * 1024 * 1024  # 10MB per chunk
MIN_MULTIPART_SIZE = 100 * 1024 * 1024  # 100MB minimum for multipart
MAX_CHUNKS_PER_UPLOAD = 1000  # Maximum chunks per upload

# Session settings
UPLOAD_SESSION_TIMEOUT = 24 * 3600  # 24 hours
CLEANUP_INTERVAL = 3600  # 1 hour

# Job processing settings
JOB_TIMEOUT = 7200  # 2 hours
JOB_RETRY_LIMIT = 3
JOB_RETRY_DELAY = 300  # 5 minutes
```

## Deployment

### Docker Compose Services

1. **MongoDB** - Database storage
2. **Redis** - Message broker and result backend
3. **Backend** - Flask API server
4. **Celery Worker** - Background task processing
5. **Celery Beat** - Periodic task scheduler
6. **Frontend** - React application

### Starting the System

```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f backend
docker-compose logs -f celery-worker
docker-compose logs -f celery-beat

# Scale workers
docker-compose up -d --scale celery-worker=3
```

### Health Checks

- MongoDB: `mongosh --eval "db.adminCommand('ping')"`
- Redis: `redis-cli ping`
- Celery: Check worker status in logs
- API: `curl http://localhost:5000/api/upload/config`

## Usage

### Frontend Integration

```javascript
import ChunkedUploader from '../utils/chunkedUpload';
import { statusManager } from '../utils/statusPoller';

// Initialize uploader
const uploader = new ChunkedUploader({
  onProgress: (progress, uploaded, total) => {
    console.log(`Upload progress: ${progress}% (${uploaded}/${total})`);
  },
  onComplete: (result) => {
    console.log('Upload completed:', result);
    // Start monitoring processing
    statusManager.monitorJob(result.task_id, {
      onStatusUpdate: (status) => console.log('Status:', status),
      onComplete: (result) => console.log('Processing done:', result)
    });
  }
});

// Start upload
await uploader.initialize(file, { coordinates, selectedModel });
await uploader.start();
```

### API Usage

```bash
# Initialize upload
curl -X POST http://localhost:5000/api/upload/initialize \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "filename": "large_video.mp4",
    "file_size": 1073741824,
    "content_type": "video/mp4",
    "coordinates": "1.234,5.678",
    "selected_model": "All"
  }'

# Upload chunk
curl -X POST http://localhost:5000/api/upload/chunk \
  -H "Authorization: Bearer $TOKEN" \
  -F "upload_id=uuid" \
  -F "chunk_number=1" \
  -F "chunk=@chunk_001.bin"

# Complete upload
curl -X POST http://localhost:5000/api/upload/complete \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "upload_id": "uuid",
    "session_id": "uuid"
  }'

# Check status
curl -X GET http://localhost:5000/api/status/job/task_id \
  -H "Authorization: Bearer $TOKEN"
```

## Monitoring

### Celery Monitoring

```bash
# Monitor workers
celery -A celery_app inspect active
celery -A celery_app inspect stats

# Monitor queues
celery -A celery_app inspect reserved
```

### Database Monitoring

```javascript
// Check upload sessions
db.upload_sessions.find({status: "IN_PROGRESS"}).count()

// Check processing jobs
db.video_processing_jobs.find({status: "PROCESSING"}).count()

// Cleanup old sessions
db.upload_sessions.deleteMany({
  expires_at: {$lt: new Date()},
  status: {$in: ["EXPIRED", "FAILED"]}
})
```

## Troubleshooting

### Common Issues

1. **Upload Fails to Initialize**
   - Check file size limits
   - Verify AWS credentials
   - Check S3 bucket permissions

2. **Chunks Fail to Upload**
   - Check network connectivity
   - Verify upload session hasn't expired
   - Check S3 multipart upload limits

3. **Processing Doesn't Start**
   - Check Celery worker status
   - Verify Redis connection
   - Check task queue

4. **Status Updates Stop**
   - Check Redis connection
   - Verify polling is active
   - Check authentication token

### Performance Tuning

1. **Upload Performance**
   - Adjust chunk size based on network conditions
   - Increase concurrent chunk uploads
   - Use CDN for better global performance

2. **Processing Performance**
   - Scale Celery workers horizontally
   - Optimize video processing algorithms
   - Use GPU acceleration if available

3. **Storage Performance**
   - Use S3 Transfer Acceleration
   - Implement intelligent tiering
   - Monitor S3 request rates

## Security Considerations

1. **File Validation**
   - Strict file type checking
   - File signature verification
   - Size limit enforcement

2. **Authentication**
   - JWT token validation
   - Session management
   - Rate limiting

3. **Storage Security**
   - S3 bucket policies
   - Encryption at rest
   - Access logging

4. **Network Security**
   - HTTPS enforcement
   - CORS configuration
   - Input sanitization
