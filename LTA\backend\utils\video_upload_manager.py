"""
Video Upload Manager
Handles chunked/resumable video uploads with S3 multipart upload support
"""
import os
import uuid
import logging
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import boto3
from botocore.exceptions import ClientError
from config.db import connect_to_db
from config.upload_config import (
    CHUNK_SIZE, MIN_MULTIPART_SIZE, UPLOAD_SESSION_TIMEOUT,
    S3_MULTIPART_THRESHOLD, S3_MULTIPART_CHUNKSIZE, S3_MAX_CONCURRENCY
)

logger = logging.getLogger(__name__)

class VideoUploadManager:
    """
    Manages chunked video uploads with S3 multipart upload support
    """
    
    def __init__(self):
        """Initialize the upload manager"""
        self.db = connect_to_db()
        self.s3_client = self._initialize_s3_client()
        self.bucket = self._get_s3_bucket()
    
    def _initialize_s3_client(self):
        """Initialize S3 client"""
        try:
            return boto3.client(
                's3',
                aws_access_key_id=os.environ.get('AWS_ACCESS_KEY_ID'),
                aws_secret_access_key=os.environ.get('AWS_SECRET_ACCESS_KEY'),
                region_name=os.environ.get('AWS_REGION', 'us-east-1')
            )
        except Exception as e:
            logger.error(f"Failed to initialize S3 client: {e}")
            return None
    
    def _get_s3_bucket(self) -> str:
        """Extract S3 bucket from AWS_FOLDER environment variable"""
        aws_folder = os.environ.get('AWS_FOLDER', 'aispry-project/2024_Oct_YNMSafety_RoadSafetyAudit/audit/raisetech')
        return aws_folder.split('/')[0]
    
    def create_upload_session(self, filename: str, file_size: int, content_type: str,
                            username: str, role: str, coordinates: str = None,
                            selected_model: str = 'All') -> Dict[str, Any]:
        """
        Create a new upload session for chunked upload
        
        Args:
            filename: Original filename
            file_size: Total file size in bytes
            content_type: MIME type of the file
            username: User uploading the file
            role: User role
            coordinates: GPS coordinates (optional)
            selected_model: Model type for processing
            
        Returns:
            Dict containing upload session details
        """
        try:
            upload_id = str(uuid.uuid4())
            session_id = str(uuid.uuid4())
            
            # Calculate number of chunks needed
            chunk_count = (file_size + CHUNK_SIZE - 1) // CHUNK_SIZE
            
            # Determine if multipart upload is needed
            use_multipart = file_size >= MIN_MULTIPART_SIZE
            
            # Create S3 key
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            s3_key = f"{role}/{username}/videos/original/{upload_id}_{timestamp}_{filename}"
            
            # Initialize S3 multipart upload if needed
            multipart_upload_id = None
            if use_multipart and self.s3_client:
                try:
                    response = self.s3_client.create_multipart_upload(
                        Bucket=self.bucket,
                        Key=s3_key,
                        ContentType=content_type,
                        Metadata={
                            'username': username,
                            'role': role,
                            'upload_id': upload_id,
                            'original_filename': filename
                        }
                    )
                    multipart_upload_id = response['UploadId']
                    logger.info(f"Created S3 multipart upload: {multipart_upload_id}")
                except ClientError as e:
                    logger.error(f"Failed to create S3 multipart upload: {e}")
                    raise Exception(f"Failed to initialize S3 multipart upload: {e}")
            
            # Create upload session document
            session_doc = {
                'upload_id': upload_id,
                'session_id': session_id,
                'filename': filename,
                'file_size': file_size,
                'content_type': content_type,
                'username': username,
                'role': role,
                'coordinates': coordinates,
                'selected_model': selected_model,
                'chunk_count': chunk_count,
                'chunk_size': CHUNK_SIZE,
                'uploaded_chunks': [],
                'use_multipart': use_multipart,
                's3_key': s3_key,
                's3_multipart_upload_id': multipart_upload_id,
                's3_parts': [],  # For multipart upload parts
                'status': 'INITIALIZED',
                'created_at': datetime.utcnow(),
                'expires_at': datetime.utcnow() + timedelta(seconds=UPLOAD_SESSION_TIMEOUT),
                'last_activity': datetime.utcnow()
            }
            
            # Store in database
            if self.db:
                result = self.db.upload_sessions.insert_one(session_doc)
                logger.info(f"Created upload session: {upload_id}")
            
            return {
                'upload_id': upload_id,
                'session_id': session_id,
                'chunk_count': chunk_count,
                'chunk_size': CHUNK_SIZE,
                'use_multipart': use_multipart,
                's3_key': s3_key,
                'multipart_upload_id': multipart_upload_id
            }
            
        except Exception as e:
            logger.error(f"Failed to create upload session: {e}")
            raise Exception(f"Failed to create upload session: {e}")
    
    def upload_chunk(self, upload_id: str, chunk_number: int, chunk_data: bytes) -> Dict[str, Any]:
        """
        Upload a single chunk
        
        Args:
            upload_id: Upload session ID
            chunk_number: Chunk number (1-based)
            chunk_data: Chunk data bytes
            
        Returns:
            Dict containing upload result
        """
        try:
            # Get upload session
            session = self._get_upload_session(upload_id)
            if not session:
                raise Exception(f"Upload session not found: {upload_id}")
            
            # Validate chunk
            if chunk_number < 1 or chunk_number > session['chunk_count']:
                raise Exception(f"Invalid chunk number: {chunk_number}")
            
            # Check if chunk already uploaded
            if chunk_number in session['uploaded_chunks']:
                logger.info(f"Chunk {chunk_number} already uploaded for {upload_id}")
                return {'status': 'already_uploaded', 'chunk_number': chunk_number}
            
            # Calculate chunk hash for integrity
            chunk_hash = hashlib.md5(chunk_data).hexdigest()
            
            result = {'status': 'uploaded', 'chunk_number': chunk_number, 'chunk_hash': chunk_hash}
            
            # Upload to S3
            if session['use_multipart'] and self.s3_client:
                # Multipart upload
                try:
                    response = self.s3_client.upload_part(
                        Bucket=self.bucket,
                        Key=session['s3_key'],
                        PartNumber=chunk_number,
                        UploadId=session['s3_multipart_upload_id'],
                        Body=chunk_data
                    )
                    
                    etag = response['ETag']
                    
                    # Update session with part info
                    self.db.upload_sessions.update_one(
                        {'upload_id': upload_id},
                        {
                            '$push': {
                                'uploaded_chunks': chunk_number,
                                's3_parts': {
                                    'PartNumber': chunk_number,
                                    'ETag': etag
                                }
                            },
                            '$set': {'last_activity': datetime.utcnow()}
                        }
                    )
                    
                    result['etag'] = etag
                    logger.info(f"Uploaded chunk {chunk_number} for {upload_id}")
                    
                except ClientError as e:
                    logger.error(f"Failed to upload chunk {chunk_number}: {e}")
                    raise Exception(f"Failed to upload chunk: {e}")
            else:
                # Store chunk temporarily for single upload
                temp_dir = os.path.join(os.path.dirname(__file__), '..', 'temp_uploads')
                os.makedirs(temp_dir, exist_ok=True)
                
                chunk_path = os.path.join(temp_dir, f"{upload_id}_chunk_{chunk_number}")
                with open(chunk_path, 'wb') as f:
                    f.write(chunk_data)
                
                # Update session
                self.db.upload_sessions.update_one(
                    {'upload_id': upload_id},
                    {
                        '$push': {'uploaded_chunks': chunk_number},
                        '$set': {'last_activity': datetime.utcnow()}
                    }
                )
                
                logger.info(f"Stored chunk {chunk_number} temporarily for {upload_id}")
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to upload chunk: {e}")
            raise Exception(f"Failed to upload chunk: {e}")
    
    def _get_upload_session(self, upload_id: str) -> Optional[Dict]:
        """Get upload session from database"""
        if not self.db:
            return None
        return self.db.upload_sessions.find_one({'upload_id': upload_id})

    def complete_upload(self, upload_id: str) -> Dict[str, Any]:
        """
        Complete the upload and finalize S3 multipart upload

        Args:
            upload_id: Upload session ID

        Returns:
            Dict containing completion result
        """
        try:
            # Get upload session
            session = self._get_upload_session(upload_id)
            if not session:
                raise Exception(f"Upload session not found: {upload_id}")

            # Check if all chunks are uploaded
            expected_chunks = set(range(1, session['chunk_count'] + 1))
            uploaded_chunks = set(session['uploaded_chunks'])

            if expected_chunks != uploaded_chunks:
                missing_chunks = expected_chunks - uploaded_chunks
                raise Exception(f"Missing chunks: {list(missing_chunks)}")

            if session['use_multipart'] and self.s3_client:
                # Complete S3 multipart upload
                try:
                    # Sort parts by part number
                    parts = sorted(session['s3_parts'], key=lambda x: x['PartNumber'])

                    response = self.s3_client.complete_multipart_upload(
                        Bucket=self.bucket,
                        Key=session['s3_key'],
                        UploadId=session['s3_multipart_upload_id'],
                        MultipartUpload={'Parts': parts}
                    )

                    s3_url = response['Location']
                    logger.info(f"Completed S3 multipart upload: {s3_url}")

                except ClientError as e:
                    logger.error(f"Failed to complete S3 multipart upload: {e}")
                    # Abort the multipart upload
                    try:
                        self.s3_client.abort_multipart_upload(
                            Bucket=self.bucket,
                            Key=session['s3_key'],
                            UploadId=session['s3_multipart_upload_id']
                        )
                    except:
                        pass
                    raise Exception(f"Failed to complete S3 upload: {e}")
            else:
                # Combine chunks and upload as single file
                temp_dir = os.path.join(os.path.dirname(__file__), '..', 'temp_uploads')
                combined_file_path = os.path.join(temp_dir, f"{upload_id}_combined")

                try:
                    with open(combined_file_path, 'wb') as combined_file:
                        for chunk_num in range(1, session['chunk_count'] + 1):
                            chunk_path = os.path.join(temp_dir, f"{upload_id}_chunk_{chunk_num}")
                            if os.path.exists(chunk_path):
                                with open(chunk_path, 'rb') as chunk_file:
                                    combined_file.write(chunk_file.read())
                                os.remove(chunk_path)  # Clean up chunk file

                    # Upload combined file to S3
                    if self.s3_client:
                        with open(combined_file_path, 'rb') as f:
                            self.s3_client.upload_fileobj(
                                f,
                                self.bucket,
                                session['s3_key'],
                                ExtraArgs={
                                    'ContentType': session['content_type'],
                                    'Metadata': {
                                        'username': session['username'],
                                        'role': session['role'],
                                        'upload_id': upload_id,
                                        'original_filename': session['filename']
                                    }
                                }
                            )

                    # Clean up combined file
                    os.remove(combined_file_path)

                except Exception as e:
                    logger.error(f"Failed to combine and upload chunks: {e}")
                    raise Exception(f"Failed to complete upload: {e}")

            # Update session status
            self.db.upload_sessions.update_one(
                {'upload_id': upload_id},
                {
                    '$set': {
                        'status': 'COMPLETED',
                        'completed_at': datetime.utcnow(),
                        'last_activity': datetime.utcnow()
                    }
                }
            )

            logger.info(f"Upload completed successfully: {upload_id}")

            return {
                'status': 'completed',
                'upload_id': upload_id,
                's3_key': session['s3_key'],
                'file_size': session['file_size']
            }

        except Exception as e:
            logger.error(f"Failed to complete upload: {e}")
            # Mark session as failed
            if self.db:
                self.db.upload_sessions.update_one(
                    {'upload_id': upload_id},
                    {'$set': {'status': 'FAILED', 'error': str(e), 'last_activity': datetime.utcnow()}}
                )
            raise Exception(f"Failed to complete upload: {e}")

    def get_upload_status(self, upload_id: str) -> Dict[str, Any]:
        """
        Get upload status and progress

        Args:
            upload_id: Upload session ID

        Returns:
            Dict containing upload status
        """
        try:
            session = self._get_upload_session(upload_id)
            if not session:
                return {'status': 'not_found', 'error': 'Upload session not found'}

            uploaded_count = len(session['uploaded_chunks'])
            total_count = session['chunk_count']
            progress = (uploaded_count / total_count) * 100 if total_count > 0 else 0

            return {
                'status': session['status'],
                'upload_id': upload_id,
                'progress': progress,
                'uploaded_chunks': uploaded_count,
                'total_chunks': total_count,
                'file_size': session['file_size'],
                'filename': session['filename'],
                'created_at': session['created_at'],
                'last_activity': session['last_activity'],
                'expires_at': session['expires_at']
            }

        except Exception as e:
            logger.error(f"Failed to get upload status: {e}")
            return {'status': 'error', 'error': str(e)}

    def cleanup_expired_sessions(self):
        """Clean up expired upload sessions"""
        try:
            if not self.db:
                return

            # Find expired sessions
            expired_sessions = self.db.upload_sessions.find({
                'expires_at': {'$lt': datetime.utcnow()},
                'status': {'$in': ['INITIALIZED', 'IN_PROGRESS']}
            })

            for session in expired_sessions:
                upload_id = session['upload_id']
                logger.info(f"Cleaning up expired session: {upload_id}")

                # Abort S3 multipart upload if exists
                if session.get('s3_multipart_upload_id') and self.s3_client:
                    try:
                        self.s3_client.abort_multipart_upload(
                            Bucket=self.bucket,
                            Key=session['s3_key'],
                            UploadId=session['s3_multipart_upload_id']
                        )
                    except ClientError as e:
                        logger.warning(f"Failed to abort multipart upload: {e}")

                # Clean up temporary chunk files
                temp_dir = os.path.join(os.path.dirname(__file__), '..', 'temp_uploads')
                for chunk_num in session.get('uploaded_chunks', []):
                    chunk_path = os.path.join(temp_dir, f"{upload_id}_chunk_{chunk_num}")
                    if os.path.exists(chunk_path):
                        try:
                            os.remove(chunk_path)
                        except OSError as e:
                            logger.warning(f"Failed to remove chunk file: {e}")

                # Update session status
                self.db.upload_sessions.update_one(
                    {'upload_id': upload_id},
                    {'$set': {'status': 'EXPIRED', 'last_activity': datetime.utcnow()}}
                )

            logger.info("Completed cleanup of expired upload sessions")

        except Exception as e:
            logger.error(f"Failed to cleanup expired sessions: {e}")

    def abort_upload(self, upload_id: str) -> Dict[str, Any]:
        """
        Abort an upload session

        Args:
            upload_id: Upload session ID

        Returns:
            Dict containing abort result
        """
        try:
            session = self._get_upload_session(upload_id)
            if not session:
                return {'status': 'not_found', 'error': 'Upload session not found'}

            # Abort S3 multipart upload if exists
            if session.get('s3_multipart_upload_id') and self.s3_client:
                try:
                    self.s3_client.abort_multipart_upload(
                        Bucket=self.bucket,
                        Key=session['s3_key'],
                        UploadId=session['s3_multipart_upload_id']
                    )
                    logger.info(f"Aborted S3 multipart upload for {upload_id}")
                except ClientError as e:
                    logger.warning(f"Failed to abort S3 multipart upload: {e}")

            # Clean up temporary files
            temp_dir = os.path.join(os.path.dirname(__file__), '..', 'temp_uploads')
            for chunk_num in session.get('uploaded_chunks', []):
                chunk_path = os.path.join(temp_dir, f"{upload_id}_chunk_{chunk_num}")
                if os.path.exists(chunk_path):
                    try:
                        os.remove(chunk_path)
                    except OSError:
                        pass

            # Update session status
            self.db.upload_sessions.update_one(
                {'upload_id': upload_id},
                {'$set': {'status': 'ABORTED', 'last_activity': datetime.utcnow()}}
            )

            return {'status': 'aborted', 'upload_id': upload_id}

        except Exception as e:
            logger.error(f"Failed to abort upload: {e}")
            return {'status': 'error', 'error': str(e)}
