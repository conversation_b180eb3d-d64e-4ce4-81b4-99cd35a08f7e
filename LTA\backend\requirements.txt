# Web Framework
flask==2.2.3
flask-cors==4.0.0
werkzeug==2.3.3

# Computer Vision & ML
opencv-python==********
ultralytics==8.3.78 
ultralytics-thop==2.0.14

scipy==1.11.3
pillow==10.0.0
timm==1.0.15
imageio==2.37.0
imageio-ffmpeg==0.6.0
pillow-avif-plugin==1.5.2

# Data Processing & Visualization
pandas==2.0.3
plotly==5.15.0
matplotlib==3.9.2

# PDF Generation
reportlab==4.0.4

# Database
pymongo==4.5.0
dnspython==2.7.0

# Geospatial
geopy==2.3.0
folium==0.14.0
branca==0.8.1

# Utils
python-dotenv==1.0.0
requests==2.31.0
tqdm
lxml==5.2.1
certifi==2024.7.4
charset-normalizer==3.3.2
idna==3.7
urllib3==2.2.1

boto3==1.39.7
botocore==1.39.7

# Task Queue and Background Processing
celery==5.3.4
redis==5.0.1

# WebSocket Support
flask-socketio==5.3.6
python-socketio==5.10.0


# Notes:
# - gridfs is part of pymongo, not a separate package
# - bson is part of pymongo, not a separate package
#   - uuid is a standard library module, not a package
#   - datetime is a standard library module, not a package but included for completeness 
#   pip3 install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121